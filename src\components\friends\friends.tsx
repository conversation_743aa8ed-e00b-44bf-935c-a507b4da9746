"use client";

import React from "react";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { Button } from "../ui/button";
import { Trash } from "lucide-react";
import { HoverCard, HoverCardContent, HoverCardTrigger } from "@/components/ui/hover-card";
import { useIsMobile } from "@/hooks/use-mobile";

type FriendData = {
  id: string;
  name: string;
  image: string | null;
  bio: string | null;
  sender: string;
  receiver: string;
};

type FriendsProps = {
  friends: FriendData[];
  onDenyRequest: (senderId: string, receiverId: string) => Promise<boolean>;
  loading: boolean;
};

export default ({ friends, onDenyRequest, loading }: FriendsProps) => {
  if (loading) return <div className="text-center text-muted-foreground">Loading friends...</div>;
  if (friends.length === 0)
    return <div className="text-center text-muted-foreground">No friends yet. Add some friends to get started!</div>;
  return (
    <ul className="flex flex-col gap-2 w-full">
      {friends.map((f) => (
        <HoverCard key={f.id}>
          <HoverCardTrigger asChild>
            <li className="flex items-center justify-between gap-2 p-2 rounded-lg hover:bg-muted/50 transition-colors cursor-pointer">
              <div className="flex items-center gap-2">
                <Avatar>
                  <AvatarImage src={f.image || ""} alt={`${f.name}'s profile picture`} />
                  <AvatarFallback>{f.name?.charAt(0)?.toUpperCase() || "?"}</AvatarFallback>
                </Avatar>
                <span className="truncate font-medium">{f.name}</span>
              </div>
              <div className="flex flex-row gap-1">
                <Button
                  variant={"destructive"}
                  size={"icon"}
                  onClick={() => {
                    onDenyRequest(f.sender, f.receiver);
                  }}>
                  <Trash />
                </Button>
              </div>
            </li>
          </HoverCardTrigger>
          <HoverCardContent align="center" side="top" className="w-">
            <h2 className="text-shadow-2xs w-full">
              Bio: <span className={`text-sm ${f.bio ? "text-inherit" : "text-muted-foreground"}`}>{f.bio || "User has no bio"}</span>
            </h2>
          </HoverCardContent>
        </HoverCard>
      ))}
    </ul>
  );
};
